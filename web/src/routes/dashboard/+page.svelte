<script lang="ts">
  import type { Experiment, Workspace } from "$lib/types";

  let workspaces = $state<Workspace[]>([]);
  let loading = $state({
    workspaces: true,
    experiments: false,
    experimentDetails: false,
  });
  let errors = $state({
    workspaces: null as string | null,
    experiments: null as string | null,
    experimentDetails: null as string | null,
  });

  let selectedWorkspace = $state<Workspace | null>(null);
  let selectedExperiment = $state<Experiment | null>(null);
  let experiments = $state<Experiment[]>([]);
  let scalarMetrics = $state<any[]>([]);

  let workspaceSearchQuery = $state("");
  let experimentSearchQuery = $state("");

  async function loadWorkspaces() {
    try {
      loading.workspaces = true;
      errors.workspaces = null;
      const response = await fetch("/api/dashboard/overview");
      if (!response.ok)
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      const apiResponse = await response.json();
      const data = apiResponse.data;
      if (!data || !data.workspaces)
        throw new Error("Invalid response structure from dashboard API");

      workspaces = data.workspaces.map((ws: any) => ({
        id: ws.id,
        name: ws.name,
        description: ws.description,
        createdAt: new Date(ws.created_at),
        role: ws.role,
        experimentCount: ws.experiment_count,
      }));
      if (workspaces.length > 0 && !selectedWorkspace) {
        selectedWorkspace = workspaces[0];
      }
    } catch (error) {
      console.error("Failed to load workspaces:", error);
      errors.workspaces =
        error instanceof Error ? error.message : "Failed to load workspaces";
    } finally {
      loading.workspaces = false;
    }
  }

  async function loadExperiments(workspaceId: string) {
    try {
      loading.experiments = true;
      errors.experiments = null;
      const response = await fetch(
        `/api/workspaces/${workspaceId}/experiments`,
      );
      if (!response.ok)
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      const apiResponse = await response.json();
      const data = apiResponse.data;
      if (!data || !Array.isArray(data))
        throw new Error("Invalid response structure from experiments API");

      experiments = data.map((exp: any) => ({
        id: exp.id,
        name: exp.name,
        description: exp.description || "",
        hyperparams: exp.hyperparams || [],
        tags: exp.tags || [],
        createdAt: new Date(exp.created_at),
        updatedAt: new Date(exp.updated_at),
        availableMetrics: exp.available_metrics || [],
        workspaceId: workspaceId,
      }));
    } catch (error) {
      console.error("Failed to load experiments:", error);
      errors.experiments =
        error instanceof Error ? error.message : "Failed to load experiments";
    } finally {
      loading.experiments = false;
    }
  }

  async function loadExperimentDetails(experimentId: string) {
    try {
      loading.experimentDetails = true;
      errors.experimentDetails = null;
      const response = await fetch(`/api/experiments/${experimentId}/metrics`);
      if (!response.ok)
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      const apiResponse = await response.json();
      const metrics = apiResponse.data;
      if (!metrics || !Array.isArray(metrics))
        throw new Error("Invalid response structure from metrics API");

      scalarMetrics = metrics;
    } catch (error) {
      console.error("Failed to load experiment details:", error);
      errors.experimentDetails =
        error instanceof Error
          ? error.message
          : "Failed to load experiment details";
    } finally {
      loading.experimentDetails = false;
    }
  }

  $effect(() => {
    loadWorkspaces();
  });

  $effect(() => {
    if (selectedWorkspace) {
      loadExperiments(selectedWorkspace.id);
      selectedExperiment = null;
      scalarMetrics = [];
    }
  });

  $effect(() => {
    if (selectedExperiment) {
      loadExperimentDetails(selectedExperiment.id);
    }
  });

  function onWorkspaceSelect(workspace: Workspace) {
    selectedWorkspace = workspace;
  }
  function onExperimentSelect(experiment: Experiment) {
    selectedExperiment = experiment;
  }
  function copyToClipboard(text: string) {
    navigator.clipboard.writeText(text);
  }
  function formatDate(date: Date): string {
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year:
        date.getFullYear() !== new Date().getFullYear() ? "numeric" : undefined,
    });
  }
</script>

<div class="h-screen w-full bg-ctp-base text-ctp-text flex min-h-0">
  <!-- =================================================================== -->
  <!-- Workspaces Panel (Column 1) - 25%                                   -->
  <!-- =================================================================== -->
  <div class="w-1/4 border-r border-ctp-surface0 flex flex-col min-h-0">
    <!-- Header -->
    <div class="border-b border-ctp-surface0 p-6 flex-shrink-0">
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-ctp-text font-semibold text-lg">Workspaces</h2>
        <span class="bg-ctp-surface0 text-ctp-subtext0 px-2 py-1 text-sm"
          >{workspaces.length}</span
        >
      </div>
      <input
        type="text"
        bind:value={workspaceSearchQuery}
        placeholder="Search workspaces..."
        class="w-full bg-ctp-surface0 border border-ctp-surface1 px-4 py-2 text-sm text-ctp-text placeholder-ctp-subtext0 focus:border-ctp-blue focus:outline-none transition-colors"
      />
    </div>

    <!-- Scrollable Content -->
    <div class="flex-1 overflow-y-auto p-4 min-h-0">
      {#if loading.workspaces}
        <div class="text-center py-8 text-ctp-subtext0">
          Loading workspaces...
        </div>
      {:else if errors.workspaces}
        <div class="surface-layer-2 p-4">
          <div class="text-ctp-red font-medium mb-2">
            Error loading workspaces
          </div>
          <div class="text-ctp-subtext0 text-sm mb-3">{errors.workspaces}</div>
          <button
            class="text-ctp-blue hover:text-ctp-blue/80 text-sm font-medium"
            onclick={() => loadWorkspaces()}>Retry</button
          >
        </div>
      {:else if workspaces.length === 0}
        <div class="text-center py-8 text-ctp-subtext0">
          No workspaces found
        </div>
      {:else}
        <div class="space-y-1">
          {#each workspaces.filter((w) => w.name
              .toLowerCase()
              .includes(workspaceSearchQuery.toLowerCase())) as workspace (workspace.id)}
            <button
              class="w-full text-left p-4 transition-all {selectedWorkspace?.id ===
              workspace.id
                ? 'surface-layer-2 text-ctp-mauve'
                : 'hover:surface-layer-1'}"
              onclick={() => onWorkspaceSelect(workspace)}
            >
              <div class="flex items-center justify-between mb-2">
                <span class="font-medium">{workspace.name}</span>
                <span class="text-sm text-ctp-subtext0"
                  >{workspace.experimentCount || 0}</span
                >
              </div>
              {#if workspace.description}
                <div class="text-sm text-ctp-subtext0 line-clamp-2 mb-2">
                  {workspace.description}
                </div>
              {/if}
              <div class="text-xs text-ctp-overlay0">{workspace.role}</div>
            </button>
          {/each}
        </div>
      {/if}
    </div>
  </div>

  <!-- =================================================================== -->
  <!-- Experiments Panel (Column 2) - 25%                                  -->
  <!-- =================================================================== -->
  <div class="w-1/4 border-r border-ctp-surface0 flex flex-col min-h-0">
    <!-- Header -->
    <div class="border-b border-ctp-surface0 p-6 flex-shrink-0">
      {#if selectedWorkspace}
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-ctp-text font-semibold text-lg">Experiments</h2>
          <span class="bg-ctp-surface0 text-ctp-subtext0 px-2 py-1 text-sm"
            >{experiments.length}</span
          >
        </div>
        <input
          type="text"
          bind:value={experimentSearchQuery}
          placeholder="Search experiments..."
          class="w-full bg-ctp-surface0 border border-ctp-surface1 px-4 py-2 text-sm text-ctp-text placeholder-ctp-subtext0 focus:border-ctp-blue focus:outline-none transition-colors"
        />
      {:else}
        <div class="text-ctp-subtext0">
          Select a workspace to view experiments
        </div>
      {/if}
    </div>

    <!-- Scrollable Content -->
    <div class="flex-1 overflow-y-auto p-4 min-h-0">
      {#if selectedWorkspace}
        {#if loading.experiments}
          <div class="text-center py-8 text-ctp-subtext0">
            Loading experiments...
          </div>
        {:else if errors.experiments}
          <div class="surface-layer-2 p-4">
            <div class="text-ctp-red font-medium mb-2">
              Error loading experiments
            </div>
            <div class="text-ctp-subtext0 text-sm mb-3">
              {errors.experiments}
            </div>
            <button
              class="text-ctp-blue hover:text-ctp-blue/80 text-sm font-medium"
              onclick={() =>
                selectedWorkspace && loadExperiments(selectedWorkspace.id)}
              >Retry</button
            >
          </div>
        {:else if experiments.length === 0}
          <div class="text-center py-8 text-ctp-subtext0">
            No experiments found
          </div>
        {:else}
          <div class="space-y-1">
            {#each experiments.filter((e) => e.name
                .toLowerCase()
                .includes(experimentSearchQuery.toLowerCase())) as experiment (experiment.id)}
              <button
                class="w-full text-left p-4 transition-all {selectedExperiment?.id ===
                experiment.id
                  ? 'surface-layer-2 text-ctp-mauve'
                  : 'hover:surface-layer-1'}"
                onclick={() => onExperimentSelect(experiment)}
              >
                <div class="flex items-center justify-between mb-2">
                  <span class="font-medium">{experiment.name}</span>
                  <span class="text-sm text-ctp-subtext0"
                    >{formatDate(experiment.createdAt)}</span
                  >
                </div>
                {#if experiment.description}
                  <div class="text-sm text-ctp-subtext0 line-clamp-2 mb-2">
                    {experiment.description}
                  </div>
                {/if}
                <div
                  class="flex items-center space-x-3 text-xs text-ctp-overlay0"
                >
                  {#if experiment.tags?.length}<span
                      >{experiment.tags.length} tags</span
                    >{/if}
                  {#if experiment.availableMetrics?.length}<span
                      >{experiment.availableMetrics.length} metrics</span
                    >{/if}
                </div>
              </button>
            {/each}
          </div>
        {/if}
      {/if}
    </div>
  </div>

  <!-- =================================================================== -->
  <!-- Details Panel (Column 3) - 50%                                      -->
  <!-- =================================================================== -->
  <div class="w-1/2 flex flex-col min-h-0">
    <!-- Header -->
    <div class="border-b border-ctp-surface0 p-6 flex-shrink-0">
      {#if selectedExperiment}
        <div class="mb-4">
          <h2 class="text-ctp-text font-semibold text-xl mb-2">
            {selectedExperiment.name}
          </h2>
          {#if selectedExperiment.description}
            <p class="text-ctp-subtext0 line-clamp-2 mb-3">
              {selectedExperiment.description}
            </p>
          {/if}
          <button
            class="text-sm text-ctp-overlay0 hover:text-ctp-blue transition-colors font-mono"
            onclick={() =>
              selectedExperiment && copyToClipboard(selectedExperiment.id)}
            title="Click to copy experiment ID"
          >
            ID: {selectedExperiment.id}
          </button>
        </div>
      {:else}
        <div class="text-ctp-subtext0">
          Select an experiment to view details
        </div>
      {/if}
    </div>

    <!-- Scrollable Content -->
    <div class="flex-1 overflow-y-auto p-6 min-h-0">
      {#if selectedExperiment}
        {#if loading.experimentDetails}
          <div class="text-center py-12">
            <div class="text-ctp-subtext0 text-lg">
              Loading experiment details...
            </div>
          </div>
        {:else if errors.experimentDetails}
          <div class="surface-layer-2 p-6">
            <div class="text-ctp-red font-medium text-lg mb-3">
              Error loading experiment details
            </div>
            <div class="text-ctp-subtext0 mb-4">{errors.experimentDetails}</div>
            <button
              class="floating-element px-4 py-2 hover:surface-layer-3 transition-colors font-medium text-ctp-blue"
              onclick={() =>
                selectedExperiment &&
                loadExperimentDetails(selectedExperiment.id)}>Retry</button
            >
          </div>
        {:else}
          <div class="space-y-6">
            <!-- Metrics Section -->
            {#if scalarMetrics.length > 0}
              <div class="space-y-2">
                <div class="flex items-center gap-2">
                  <div class="text-sm text-ctp-text">metrics</div>
                  <div class="text-sm text-ctp-subtext0 font-mono">
                    [{scalarMetrics.length}]
                  </div>
                </div>
                <div class="bg-ctp-surface0/10 border border-ctp-surface0/20">
                  {#each scalarMetrics as metric}
                    <div
                      class="flex text-sm hover:bg-ctp-surface0/20 p-3 transition-colors border-b border-ctp-surface0/5"
                    >
                      <div class="w-4 text-ctp-green">•</div>
                      <div
                        class="flex-1 text-ctp-text truncate"
                        title={metric.name}
                      >
                        {metric.name}
                      </div>
                      <div
                        class="w-24 text-right text-ctp-blue font-mono"
                        title={String(metric.value)}
                      >
                        {typeof metric.value === "number"
                          ? metric.value.toFixed(4)
                          : metric.value}
                      </div>
                    </div>
                  {/each}
                </div>
              </div>
            {/if}

            <!-- Tags Section -->
            {#if selectedExperiment.tags?.length}
              <div class="space-y-2">
                <div class="flex items-center gap-2">
                  <div class="text-sm text-ctp-text">tags</div>
                  <div class="text-sm text-ctp-subtext0 font-mono">
                    [{selectedExperiment.tags.length}]
                  </div>
                </div>
                <div class="flex flex-wrap gap-1">
                  {#each selectedExperiment.tags as tag}
                    <span
                      class="text-sm bg-ctp-blue/20 text-ctp-blue border border-ctp-blue/30 px-2 py-0.5 font-mono"
                      >{tag}</span
                    >
                  {/each}
                </div>
              </div>
            {/if}

            <!-- Hyperparameters Section -->
            {#if selectedExperiment.hyperparams?.length}
              <div class="space-y-2">
                <div class="flex items-center gap-2">
                  <div class="text-sm text-ctp-text">hyperparameters</div>
                  <div class="text-sm text-ctp-subtext0 font-mono">
                    [{selectedExperiment.hyperparams.length}]
                  </div>
                </div>
                <div class="bg-ctp-surface0/10 border border-ctp-surface0/20">
                  <div class="grid grid-cols-1 lg:grid-cols-2 gap-0">
                    {#each selectedExperiment.hyperparams as param}
                      <div
                        class="flex flex-col sm:flex-row sm:items-center sm:justify-between border-b border-ctp-surface0/10 hover:bg-ctp-surface0/20 px-3 py-2 transition-colors text-sm gap-1 sm:gap-2"
                      >
                        <span class="text-ctp-subtext0 font-mono truncate"
                          >{param.key}</span
                        >
                        <span
                          class="text-ctp-blue font-mono bg-ctp-surface0/20 border border-ctp-surface0/30 px-2 py-1 max-w-32 truncate"
                          title={String(param.value)}>{param.value}</span
                        >
                      </div>
                    {/each}
                  </div>
                </div>
              </div>
            {/if}
          </div>
        {/if}
      {:else}
        <div class="flex items-center justify-center h-full">
          <div class="text-center text-ctp-subtext0">
            Select an experiment to view details
          </div>
        </div>
      {/if}
    </div>
  </div>
</div>
